<!-- Assigned Team Modal -->
<div id="assigned-team-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden transition-opacity duration-300 ease-in-out">
  <!-- Backdrop with separate opacity transition -->
  <div class="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300 ease-in-out"></div>

  <!-- Modal content with transform transition -->
  <div class="bg-white rounded-xl shadow-lg w-full max-w-lg mx-auto overflow-hidden relative transform transition-all duration-300 ease-in-out">
    <!-- Command Palette Style Interface -->
    <div class="flex h-full w-full flex-col overflow-hidden rounded-md bg-white text-gray-900">
      <!-- Search Input -->
      <div class="flex items-center border-b px-3">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4 shrink-0 opacity-50">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.3-4.3"></path>
        </svg>
        <input
          class="flex h-11 w-full rounded-md bg-transparent py-3 outline-none placeholder:text-gray-500 disabled:cursor-not-allowed disabled:opacity-50 text-sm"
          placeholder="Search for team members..."
          id="team-search-input"
          type="text"
          autocomplete="off"
          autocorrect="off"
          spellcheck="false"
        >
        <button type="button" class="text-gray-400 hover:text-gray-500 transition-colors duration-200 ml-2" id="close-assigned-team-modal">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Selected Team Members Section -->
      <div class="p-2 border-b" id="selected-team-section" style="display: none;">
        <h4 class="px-2 py-1.5 text-xs font-medium text-gray-500">Selected</h4>
        <div class="flex flex-wrap gap-2 p-2" id="selected-team-container">
          <!-- Selected team members will be populated here -->
        </div>
      </div>

      <!-- Available Team Members List -->
      <div class="overflow-y-auto overflow-x-hidden max-h-[calc(100vh-350px)] sm:max-h-[300px]">
        <div class="-mx-1 h-px bg-gray-200"></div>
        <div class="overflow-hidden text-gray-900">
          <div class="px-3 pt-5 pb-2 text-[11px] font-semibold text-gray-500 tracking-wider uppercase">Available Team Members</div>
          <div id="team-members-list">
            <% User.all.where(archived: false).order(:first_name).each do |user| %>
              <div class="relative cursor-default select-none rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-gray-50 flex items-center justify-between gap-3"
                   data-user-id="<%= user.id %>"
                   data-user-name="<%= user.full_name %>"
                   data-user-search="<%= user.full_name.downcase %>"
                   data-selected="<%= @patient.assigned_staff.include?(user) ? 'true' : 'false' %>">
                <div class="flex items-center gap-3 flex-grow">
                  <input
                    type="checkbox"
                    class="h-4 w-4 shrink-0 rounded-sm border border-gray-300 focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 mr-2 team-member-checkbox"
                    id="user-checkbox-<%= user.id %>"
                    value="<%= user.id %>"
                    <%= @patient.assigned_staff.include?(user) ? 'checked' : '' %>
                    aria-label="Select <%= user.full_name %>"
                  >
                  <span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8">
                    <% if user.image.attached? %>
                      <%= image_tag user.image.url, class: "aspect-square h-full w-full object-cover", alt: user.full_name %>
                    <% else %>
                      <div class="bg-gray-200 h-full w-full flex items-center justify-center text-gray-600 font-medium text-sm">
                        <%= user.initials %>
                      </div>
                    <% end %>
                  </span>
                  <span class="text-sm text-gray-700"><%= user.full_name %></span>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="-mx-1 h-px bg-gray-200"></div>
      <div class="p-4 border-t flex justify-end gap-2">
        <button type="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-sky-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 h-10 px-4 py-2" id="cancel-assigned-team">Cancel</button>
        <button type="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-sky-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-sky-500 text-white hover:bg-sky-600 h-10 px-4 py-2" id="save-assigned-team" data-patient-id="<%= @patient.id %>">Confirm Selection</button>
      </div>
    </div>
  </div>
</div>

<!-- Add CSS for modal transitions -->
<style>
  #assigned-team-modal:not(.hidden) {
    opacity: 1;
  }
  
  #assigned-team-modal.hidden {
    opacity: 0;
    pointer-events: none;
  }
  
  #assigned-team-modal:not(.hidden) > div:first-child {
    opacity: 1;
  }
  
  #assigned-team-modal.hidden > div:first-child {
    opacity: 0;
  }
  
  #assigned-team-modal:not(.hidden) > div:last-child {
    transform: scale(1);
  }
  
  #assigned-team-modal.hidden > div:last-child {
    transform: scale(0.95);
  }
</style>
