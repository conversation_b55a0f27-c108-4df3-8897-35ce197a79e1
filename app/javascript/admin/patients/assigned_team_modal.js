// Assigned Team Modal Functionality
document.addEventListener('DOMContentLoaded', function() {
  // Cache DOM elements
  const modal = document.getElementById('assigned-team-modal');
  const openModalBtn = document.getElementById('open-assigned-team-modal');
  const closeModalBtn = document.getElementById('close-assigned-team-modal');
  const cancelBtn = document.getElementById('cancel-assigned-team');
  const saveBtn = document.getElementById('save-assigned-team');
  const searchInput = document.getElementById('team-search-input');
  const selectedSection = document.getElementById('selected-team-section');
  const selectedContainer = document.getElementById('selected-team-container');
  const teamMembersList = document.getElementById('team-members-list');

  // Exit early if modal or open button doesn't exist
  if (!modal || !openModalBtn) return;

  // Initialize selected team members on modal open
  function initializeSelectedTeam() {
    updateSelectedTeamDisplay();
  }

  // Update the selected team members display
  function updateSelectedTeamDisplay() {
    const checkboxes = modal.querySelectorAll('.team-member-checkbox:checked');



    // Clear only the dynamically created items, keep the server-rendered ones
    const dynamicItems = selectedContainer.querySelectorAll('[data-dynamic="true"]');
    dynamicItems.forEach(item => item.remove());

    if (checkboxes.length > 0) {
      selectedSection.classList.remove('hidden');
      selectedSection.style.display = 'block';

      checkboxes.forEach(checkbox => {
        const userItem = checkbox.closest('[data-user-id]');
        const userId = userItem.dataset.userId;
        const userName = userItem.dataset.userName;
        const avatarElement = userItem.querySelector('.rounded-full');

        // Check if this user is already displayed (from server-side rendering)
        const existingBadge = selectedContainer.querySelector(`[data-remove-user="${userId}"]`);
        if (existingBadge) {
          return; // Skip if already displayed
        }

        // Create selected team member badge
        const selectedBadge = document.createElement('div');
        selectedBadge.className = 'flex items-center gap-2 p-1.5 pr-2 bg-gray-100 rounded-md text-sm';
        selectedBadge.setAttribute('data-dynamic', 'true');

        // Clone the avatar and resize it
        const avatarClone = avatarElement.cloneNode(true);
        avatarClone.className = avatarClone.className.replace('h-8 w-8', 'h-6 w-6');

        // Create the badge content
        selectedBadge.appendChild(avatarClone);

        const nameSpan = document.createElement('span');
        nameSpan.className = 'font-medium text-gray-900 whitespace-nowrap';
        nameSpan.textContent = userName;
        selectedBadge.appendChild(nameSpan);

        const removeButton = document.createElement('button');
        removeButton.className = 'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors hover:bg-red-100 h-5 w-5 text-gray-500 hover:text-red-600';
        removeButton.setAttribute('data-remove-user', userId);
        removeButton.setAttribute('aria-label', `Remove ${userName}`);
        removeButton.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3.5 w-3.5">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        `;
        selectedBadge.appendChild(removeButton);

        selectedContainer.appendChild(selectedBadge);
      });
    } else {
      // Only hide if there are no server-rendered items either
      const serverRenderedItems = selectedContainer.children.length - selectedContainer.querySelectorAll('[data-dynamic="true"]').length;
      if (serverRenderedItems === 0) {
        selectedSection.classList.add('hidden');
        selectedSection.style.display = 'none';
      }
    }
  }

  // Handle checkbox changes
  function handleCheckboxChange(event) {
    const checkbox = event.target;
    const userItem = checkbox.closest('[data-user-id]');
    userItem.dataset.selected = checkbox.checked ? 'true' : 'false';
    updateSelectedTeamDisplay();
  }

  // Handle remove user from selected
  function handleRemoveUser(event) {
    if (event.target.closest('[data-remove-user]')) {
      const removeBtn = event.target.closest('[data-remove-user]');
      const userId = removeBtn.dataset.removeUser;
      const checkbox = modal.querySelector(`#user-checkbox-${userId}`);
      if (checkbox) {
        checkbox.checked = false;
        checkbox.closest('[data-user-id]').dataset.selected = 'false';

        // Remove the badge (both server-rendered and dynamic)
        const badge = removeBtn.closest('.flex.items-center.gap-2');
        if (badge) {
          badge.remove();
        }

        // Update the display to handle any remaining items
        updateSelectedTeamDisplay();
      }
    }
  }

  // Search functionality
  function handleSearch(event) {
    const searchTerm = event.target.value.toLowerCase();
    const userItems = teamMembersList.querySelectorAll('[data-user-id]');

    userItems.forEach(item => {
      const searchText = item.dataset.userSearch;
      if (searchText.includes(searchTerm)) {
        item.style.display = 'flex';
      } else {
        item.style.display = 'none';
      }
    });
  }

  // Event listeners

  // Add event listeners for checkboxes
  modal.addEventListener('change', function(event) {
    if (event.target.classList.contains('team-member-checkbox')) {
      handleCheckboxChange(event);
    }
  });

  // Add event listener for remove buttons
  selectedContainer.addEventListener('click', handleRemoveUser);

  // Add search functionality
  if (searchInput) {
    searchInput.addEventListener('input', handleSearch);
  }

  // Modal open/close functionality with optimized transitions

  // Open modal with performance optimizations
  openModalBtn.addEventListener('click', function() {
    // Use requestAnimationFrame for smoother animation
    requestAnimationFrame(() => {
      modal.classList.remove('hidden');
      document.body.classList.add('overflow-hidden');

      // Small delay to ensure DOM is ready
      setTimeout(() => {
        // Initialize the selected team display
        initializeSelectedTeam();
      }, 50);

      // Focus search input
      if (searchInput) {
        searchInput.focus();
      }
    });
  });

  // Optimized close modal function
  const closeModal = function() {
    // Use requestAnimationFrame for smoother animation
    requestAnimationFrame(() => {
      modal.classList.add('hidden');
      document.body.classList.remove('overflow-hidden');
      // Clear search
      if (searchInput) {
        searchInput.value = '';
        handleSearch({ target: { value: '' } });
      }
    });
  };

  // Close with X button
  if (closeModalBtn) {
    closeModalBtn.addEventListener('click', closeModal);
  }

  // Close with Cancel button
  if (cancelBtn) {
    cancelBtn.addEventListener('click', closeModal);
  }

  // Close when clicking outside modal - with debounce to prevent multiple triggers
  let isClosing = false;
  modal.addEventListener('click', function(event) {
    if (event.target === modal && !isClosing) {
      isClosing = true;
      closeModal();
      // Reset flag after animation completes
      setTimeout(() => { isClosing = false; }, 300);
    }
  });

  // Save changes with optimized AJAX handling
  if (saveBtn) {
    saveBtn.addEventListener('click', function() {
      const patientId = this.dataset.patientId;
      // Get selected staff IDs from checked checkboxes
      const selectedCheckboxes = modal.querySelectorAll('.team-member-checkbox:checked');
      const selectedStaff = Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
      
      // Prevent multiple submissions
      if (saveBtn.disabled) return;
      
      // Show loading state
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<span class="inline-block animate-spin mr-2">↻</span> Saving...';
      
      // Make AJAX request to update assigned staff
      $.ajax({
        url: `/admin/patients/${patientId}/update_assigned_staff`,
        method: 'PATCH',
        data: {
          patient: {
            assigned_staff_ids: selectedStaff
          },
          authenticity_token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          // Show success message
          toastr.success('Team members updated successfully');
          
          // Update the UI to reflect changes - with performance optimization
          if (response.html) {
            // Use requestAnimationFrame for smoother DOM updates
            requestAnimationFrame(() => {
              $('.assigned-team-members').html(response.html);
            });
          }
          
          // Close modal after a short delay to ensure UI updates complete
          setTimeout(closeModal, 100);
        },
        error: function(xhr) {
          // Show error message
          toastr.error(xhr.responseJSON?.error || 'Failed to update team members');
        },
        complete: function() {
          // Reset button state
          setTimeout(() => {
            saveBtn.disabled = false;
            saveBtn.innerHTML = 'Confirm Selection';
          }, 300);
        }
      });
    });
  }
});
